import { useState } from 'react';
import { UserData } from '../types/user';
import { UserProfile } from './UserProfile';
import { DocumentsSection } from './DocumentsSection';
import { ExperienceSection } from './ExperienceSection';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { 
  User, 
  FileText, 
  Briefcase, 
  Settings, 
  Bell,
  LayoutDashboard,
  Menu,
  X
} from 'lucide-react';

interface UserDashboardProps {
  userData: UserData;
}

type ActiveSection = 'overview' | 'profile' | 'documents' | 'experience';

export function UserDashboard({ userData }: UserDashboardProps) {
  const [activeSection, setActiveSection] = useState<ActiveSection>('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const navigationItems = [
    { id: 'overview' as ActiveSection, label: 'Overview', icon: LayoutDashboard },
    { id: 'profile' as ActiveSection, label: 'Profile', icon: User },
    { id: 'documents' as ActiveSection, label: 'Documents', icon: FileText },
    { id: 'experience' as ActiveSection, label: 'Experience', icon: Briefcase },
  ];

  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-8 w-8 text-blue-600" />
                    <div>
                      <p className="text-2xl font-bold">{userData.documents.length}</p>
                      <p className="text-sm text-muted-foreground">Documents</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Briefcase className="h-8 w-8 text-green-600" />
                    <div>
                      <p className="text-2xl font-bold">{userData.workExperience.length}</p>
                      <p className="text-sm text-muted-foreground">Work Experience</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <User className="h-8 w-8 text-purple-600" />
                    <div>
                      <p className="text-2xl font-bold">{userData.skills.length}</p>
                      <p className="text-sm text-muted-foreground">Skills</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <UserProfile profile={userData.profile} />
          </div>
        );
      case 'profile':
        return <UserProfile profile={userData.profile} />;
      case 'documents':
        return <DocumentsSection documents={userData.documents} />;
      case 'experience':
        return (
          <ExperienceSection 
            workExperience={userData.workExperience}
            education={userData.education}
            skills={userData.skills}
          />
        );
      default:
        return <UserProfile profile={userData.profile} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              <h1 className="text-xl font-semibold">User Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar */}
          <aside className={`
            ${sidebarOpen ? 'block' : 'hidden'} lg:block
            w-64 flex-shrink-0
          `}>
            <Card>
              <CardContent className="p-4">
                <nav className="space-y-2">
                  {navigationItems.map((item) => {
                    const IconComponent = item.icon;
                    return (
                      <Button
                        key={item.id}
                        variant={activeSection === item.id ? "default" : "ghost"}
                        className="w-full justify-start"
                        onClick={() => {
                          setActiveSection(item.id);
                          setSidebarOpen(false);
                        }}
                      >
                        <IconComponent className="h-4 w-4 mr-2" />
                        {item.label}
                      </Button>
                    );
                  })}
                </nav>
              </CardContent>
            </Card>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {renderContent()}
          </main>
        </div>
      </div>
    </div>
  );
}
