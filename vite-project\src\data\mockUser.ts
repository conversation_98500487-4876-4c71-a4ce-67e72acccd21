import { UserData } from '../types/user';

export const mockUserData: UserData = {
  profile: {
    id: '1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    bio: 'Experienced software developer with a passion for creating innovative solutions and leading development teams.',
    location: 'San Francisco, CA',
    joinDate: '2022-01-15',
    status: 'active'
  },
  documents: [
    {
      id: '1',
      name: '<PERSON>_<PERSON><PERSON>_Resume_2024.pdf',
      type: 'application/pdf',
      size: 245760,
      uploadDate: '2024-01-15',
      category: 'resume'
    },
    {
      id: '2',
      name: 'AWS_Certification.pdf',
      type: 'application/pdf',
      size: 1048576,
      uploadDate: '2024-02-20',
      category: 'certificate'
    },
    {
      id: '3',
      name: 'Portfolio_Projects.zip',
      type: 'application/zip',
      size: 15728640,
      uploadDate: '2024-03-10',
      category: 'portfolio'
    },
    {
      id: '4',
      name: 'Cover_Letter.docx',
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      size: 51200,
      uploadDate: '2024-01-20',
      category: 'other'
    }
  ],
  workExperience: [
    {
      id: '1',
      company: 'TechCorp Solutions',
      position: 'Senior Software Engineer',
      startDate: '2022-03-01',
      current: true,
      description: 'Lead development of microservices architecture, mentor junior developers, and collaborate with cross-functional teams to deliver high-quality software solutions.',
      location: 'San Francisco, CA'
    },
    {
      id: '2',
      company: 'StartupXYZ',
      position: 'Full Stack Developer',
      startDate: '2020-06-15',
      endDate: '2022-02-28',
      current: false,
      description: 'Developed and maintained web applications using React, Node.js, and MongoDB. Implemented CI/CD pipelines and improved application performance by 40%.',
      location: 'Remote'
    },
    {
      id: '3',
      company: 'Digital Agency Inc',
      position: 'Frontend Developer',
      startDate: '2019-01-10',
      endDate: '2020-06-10',
      current: false,
      description: 'Created responsive web interfaces for various clients using modern JavaScript frameworks and CSS preprocessors.',
      location: 'New York, NY'
    }
  ],
  education: [
    {
      id: '1',
      institution: 'University of California, Berkeley',
      degree: 'Bachelor of Science',
      field: 'Computer Science',
      startDate: '2015-08-01',
      endDate: '2019-05-15',
      current: false,
      gpa: '3.8',
      description: 'Focused on software engineering, algorithms, and data structures. Participated in hackathons and coding competitions.'
    },
    {
      id: '2',
      institution: 'Stanford University',
      degree: 'Master of Science',
      field: 'Software Engineering',
      startDate: '2023-09-01',
      current: true,
      description: 'Currently pursuing advanced studies in distributed systems and machine learning applications.'
    }
  ],
  skills: [
    { id: '1', name: 'JavaScript', level: 'expert', category: 'technical' },
    { id: '2', name: 'TypeScript', level: 'advanced', category: 'technical' },
    { id: '3', name: 'React', level: 'expert', category: 'technical' },
    { id: '4', name: 'Node.js', level: 'advanced', category: 'technical' },
    { id: '5', name: 'Python', level: 'intermediate', category: 'technical' },
    { id: '6', name: 'AWS', level: 'advanced', category: 'technical' },
    { id: '7', name: 'Docker', level: 'intermediate', category: 'technical' },
    { id: '8', name: 'Leadership', level: 'advanced', category: 'soft' },
    { id: '9', name: 'Communication', level: 'expert', category: 'soft' },
    { id: '10', name: 'Problem Solving', level: 'expert', category: 'soft' },
    { id: '11', name: 'Spanish', level: 'intermediate', category: 'language' },
    { id: '12', name: 'French', level: 'beginner', category: 'language' }
  ]
};
