import { WorkExperience, Education, Skill } from '../types/user';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Briefcase, GraduationCap, Award, MapPin, Calendar } from 'lucide-react';

interface ExperienceSectionProps {
  workExperience: WorkExperience[];
  education: Education[];
  skills: Skill[];
}

export function ExperienceSection({ workExperience, education, skills }: ExperienceSectionProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  };

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'expert':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'advanced':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'beginner':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const getSkillCategoryColor = (category: string) => {
    switch (category) {
      case 'technical':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
      case 'soft':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-100';
      case 'language':
        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-100';
      case 'other':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const groupSkillsByCategory = (skills: Skill[]) => {
    return skills.reduce((acc, skill) => {
      if (!acc[skill.category]) {
        acc[skill.category] = [];
      }
      acc[skill.category].push(skill);
      return acc;
    }, {} as Record<string, Skill[]>);
  };

  const skillsByCategory = groupSkillsByCategory(skills);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-bold">Experience & Skills</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="work" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="work">Work Experience</TabsTrigger>
            <TabsTrigger value="education">Education</TabsTrigger>
            <TabsTrigger value="skills">Skills</TabsTrigger>
          </TabsList>

          <TabsContent value="work" className="mt-6">
            <div className="space-y-6">
              {workExperience.length === 0 ? (
                <div className="text-center py-8">
                  <Briefcase className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No work experience added yet</p>
                </div>
              ) : (
                workExperience.map((job, index) => (
                  <div key={job.id}>
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        <Briefcase className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{job.position}</h3>
                            <p className="text-muted-foreground font-medium">{job.company}</p>
                          </div>
                          {job.current && (
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                              Current
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>
                              {formatDate(job.startDate)} - {job.current ? 'Present' : formatDate(job.endDate!)}
                            </span>
                          </div>
                          {job.location && (
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-4 w-4" />
                              <span>{job.location}</span>
                            </div>
                          )}
                        </div>
                        <p className="text-sm leading-relaxed">{job.description}</p>
                      </div>
                    </div>
                    {index < workExperience.length - 1 && <Separator className="mt-6" />}
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="education" className="mt-6">
            <div className="space-y-6">
              {education.length === 0 ? (
                <div className="text-center py-8">
                  <GraduationCap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No education information added yet</p>
                </div>
              ) : (
                education.map((edu, index) => (
                  <div key={edu.id}>
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        <GraduationCap className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="text-lg font-semibold">{edu.degree} in {edu.field}</h3>
                            <p className="text-muted-foreground font-medium">{edu.institution}</p>
                          </div>
                          {edu.current && (
                            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                              In Progress
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>
                              {formatDate(edu.startDate)} - {edu.current ? 'Present' : formatDate(edu.endDate!)}
                            </span>
                          </div>
                          {edu.gpa && (
                            <div className="flex items-center space-x-1">
                              <Award className="h-4 w-4" />
                              <span>GPA: {edu.gpa}</span>
                            </div>
                          )}
                        </div>
                        {edu.description && (
                          <p className="text-sm leading-relaxed">{edu.description}</p>
                        )}
                      </div>
                    </div>
                    {index < education.length - 1 && <Separator className="mt-6" />}
                  </div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="skills" className="mt-6">
            <div className="space-y-6">
              {Object.keys(skillsByCategory).length === 0 ? (
                <div className="text-center py-8">
                  <Award className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No skills added yet</p>
                </div>
              ) : (
                Object.entries(skillsByCategory).map(([category, categorySkills]) => (
                  <div key={category}>
                    <h3 className="text-lg font-semibold mb-3 capitalize">
                      {category} Skills
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {categorySkills.map((skill) => (
                        <div key={skill.id} className="flex items-center space-x-2">
                          <Badge variant="outline" className={getSkillCategoryColor(skill.category)}>
                            {skill.name}
                          </Badge>
                          <Badge className={getSkillLevelColor(skill.level)}>
                            {skill.level}
                          </Badge>
                        </div>
                      ))}
                    </div>
                    <Separator className="mt-4" />
                  </div>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
